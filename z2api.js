#!/usr/bin/env node

/**
 * Z.AI Proxy - OpenAI-compatible API for Z.AI (Node.js Single File Version)
 * 基于原Python项目的完整功能实现
 * 只使用Node.js内置模块，无第三方依赖1
 */

const http = require('http');
const https = require('https');
const url = require('url');
const crypto = require('crypto');
const querystring = require('querystring');
const fs = require('fs');
const path = require('path');

// ==================== 环境变量加载 ====================
// 手动实现.env文件解析（替代dotenv）
function loadEnvFile() {
    // 检查是否禁用.env文件加载
    if (process.env.DISABLE_DOTENV === 'true' || process.env.DISABLE_DOTENV === '1') {
        console.log('ℹ️  .env file loading disabled by DISABLE_DOTENV environment variable');
        return;
    }

    try {
        const envPath = path.join(process.cwd(), '.env');
        if (fs.existsSync(envPath)) {
            const envContent = fs.readFileSync(envPath, 'utf8');
            const lines = envContent.split('\n');

            for (const line of lines) {
                const trimmedLine = line.trim();

                // 跳过空行和注释行
                if (!trimmedLine || trimmedLine.startsWith('#')) {
                    continue;
                }

                // 解析键值对
                const equalIndex = trimmedLine.indexOf('=');
                if (equalIndex > 0) {
                    const key = trimmedLine.substring(0, equalIndex).trim();
                    let value = trimmedLine.substring(equalIndex + 1).trim();

                    // 移除引号
                    if ((value.startsWith('"') && value.endsWith('"')) ||
                        (value.startsWith("'") && value.endsWith("'"))) {
                        value = value.slice(1, -1);
                    }

                    // 只有当环境变量不存在时才设置
                    if (!process.env.hasOwnProperty(key)) {
                        process.env[key] = value;
                    }
                }
            }
            console.log('✅ Loaded environment variables from .env file');
        } else {
            console.log('ℹ️  No .env file found, using system environment variables only');
        }
    } catch (error) {
        console.log(`⚠️  Warning: Could not load .env file: ${error.message}`);
    }
}

// 加载.env文件
loadEnvFile();

// ==================== 代理配置 ====================
// 解析代理配置
function parseProxyConfig() {
    const proxyConfig = {
        enabled: false,
        host: null,
        port: null,
        auth: null,
        rejectUnauthorized: true
    };

    // 支持多种代理环境变量格式
    const proxyUrl = process.env.HTTP_PROXY ||
                    process.env.http_proxy ||
                    process.env.HTTPS_PROXY ||
                    process.env.https_proxy;

    if (proxyUrl) {
        try {
            const parsed = new URL(proxyUrl);
            proxyConfig.enabled = true;
            proxyConfig.host = parsed.hostname;
            proxyConfig.port = parseInt(parsed.port) || (parsed.protocol === 'https:' ? 443 : 80);

            // 解析认证信息
            if (parsed.username && parsed.password) {
                proxyConfig.auth = `${parsed.username}:${parsed.password}`;
            }

            // 检查是否禁用TLS验证
            const disableTLS = process.env.DISABLE_TLS_VERIFY || process.env.NODE_TLS_REJECT_UNAUTHORIZED;
            if (disableTLS && ['false', '0', 'no'].includes(disableTLS.toLowerCase())) {
                proxyConfig.rejectUnauthorized = false;
                console.log(`⚠️  TLS certificate verification disabled`);
            }

            console.log(`✅ HTTP Proxy configured: ${proxyConfig.host}:${proxyConfig.port}`);
            if (proxyConfig.auth) {
                console.log(`   Proxy authentication: enabled`);
            }
            if (!proxyConfig.rejectUnauthorized) {
                console.log(`   TLS verification: disabled`);
            }
        } catch (error) {
            console.log(`⚠️  Invalid proxy URL: ${proxyUrl}`);
        }
    }

    return proxyConfig;
}

const proxyConfig = parseProxyConfig();

// ==================== 工具函数 ====================
// UUID生成函数（替代uuid包）
function generateUUID() {
    return crypto.randomUUID();
}

// JSON解析中间件
function parseJSON(req, callback) {
    let body = '';
    req.on('data', chunk => {
        body += chunk.toString();
    });
    req.on('end', () => {
        try {
            req.body = body ? JSON.parse(body) : {};
            callback();
        } catch (error) {
            callback(error);
        }
    });
}

// CORS头部设置
function setCORSHeaders(res) {
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Max-Age', '86400');
}

// HTTP请求函数（替代axios）
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        let requestOptions = { ...options };
        let requestModule = https;

        // 如果启用了代理
        if (proxyConfig.enabled) {
            // 使用HTTP模块连接到代理服务器
            requestModule = http;
            requestOptions = {
                hostname: proxyConfig.host,
                port: proxyConfig.port,
                path: `https://${options.hostname}:${options.port || 443}${options.path}`,
                method: 'CONNECT',
                headers: {}
            };

            // 添加代理认证
            if (proxyConfig.auth) {
                requestOptions.headers['Proxy-Authorization'] = `Basic ${Buffer.from(proxyConfig.auth).toString('base64')}`;
            }

            // 建立CONNECT隧道
            const connectReq = requestModule.request(requestOptions);

            connectReq.on('connect', (res, socket, head) => {
                if (res.statusCode === 200) {
                    // 隧道建立成功，通过隧道发送HTTPS请求
                    const httpsOptions = {
                        ...options,
                        socket: socket,
                        rejectUnauthorized: proxyConfig.rejectUnauthorized
                    };

                    const req = https.request(httpsOptions, (res) => {
                        let responseData = '';

                        res.on('data', (chunk) => {
                            responseData += chunk;
                        });

                        res.on('end', () => {
                            resolve({
                                status: res.statusCode,
                                statusText: res.statusMessage,
                                headers: res.headers,
                                data: responseData
                            });
                        });
                    });

                    req.on('error', reject);

                    if (data) {
                        req.write(typeof data === 'string' ? data : JSON.stringify(data));
                    }

                    req.end();
                } else {
                    reject(new Error(`Proxy connection failed: ${res.statusCode}`));
                }
            });

            connectReq.on('error', reject);
            connectReq.end();
        } else {
            // 直接连接（无代理）
            if (!proxyConfig.rejectUnauthorized) {
                requestOptions.rejectUnauthorized = false;
            }
            const req = requestModule.request(requestOptions, (res) => {
                let responseData = '';

                res.on('data', (chunk) => {
                    responseData += chunk;
                });

                res.on('end', () => {
                    resolve({
                        status: res.statusCode,
                        statusText: res.statusMessage,
                        headers: res.headers,
                        data: responseData
                    });
                });
            });

            req.on('error', reject);

            if (data) {
                req.write(typeof data === 'string' ? data : JSON.stringify(data));
            }

            req.end();
        }
    });
}

// 流式HTTP请求函数
function makeStreamRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        let requestOptions = { ...options };
        let requestModule = https;

        // 如果启用了代理
        if (proxyConfig.enabled) {
            // 使用HTTP模块连接到代理服务器
            requestModule = http;
            requestOptions = {
                hostname: proxyConfig.host,
                port: proxyConfig.port,
                path: `https://${options.hostname}:${options.port || 443}${options.path}`,
                method: 'CONNECT',
                headers: {}
            };

            // 添加代理认证
            if (proxyConfig.auth) {
                requestOptions.headers['Proxy-Authorization'] = `Basic ${Buffer.from(proxyConfig.auth).toString('base64')}`;
            }

            // 建立CONNECT隧道
            const connectReq = requestModule.request(requestOptions);

            connectReq.on('connect', (res, socket, head) => {
                if (res.statusCode === 200) {
                    // 隧道建立成功，通过隧道发送HTTPS请求
                    const httpsOptions = {
                        ...options,
                        socket: socket,
                        rejectUnauthorized: proxyConfig.rejectUnauthorized
                    };

                    const req = https.request(httpsOptions, (res) => {
                        resolve({
                            status: res.statusCode,
                            statusText: res.statusMessage,
                            headers: res.headers,
                            data: res // 返回流对象
                        });
                    });

                    req.on('error', reject);

                    if (data) {
                        req.write(typeof data === 'string' ? data : JSON.stringify(data));
                    }

                    req.end();
                } else {
                    reject(new Error(`Proxy connection failed: ${res.statusCode}`));
                }
            });

            connectReq.on('error', reject);
            connectReq.end();
        } else {
            // 直接连接（无代理）
            if (!proxyConfig.rejectUnauthorized) {
                requestOptions.rejectUnauthorized = false;
            }
            const req = requestModule.request(requestOptions, (res) => {
                resolve({
                    status: res.statusCode,
                    statusText: res.statusMessage,
                    headers: res.headers,
                    data: res // 返回流对象
                });
            });

            req.on('error', reject);

            if (data) {
                req.write(typeof data === 'string' ? data : JSON.stringify(data));
            }

            req.end();
        }
    });
}

// ==================== 配置管理 ====================
class Settings {
    constructor() {
        this.HOST = process.env.HOST || '0.0.0.0';
        this.PORT = parseInt(process.env.PORT) || 8000;
        this.UPSTREAM_URL = 'https://chat.z.ai/api/chat/completions';
        this.UPSTREAM_MODEL = '0727-360B-API';
        this.MODEL_NAME = 'GLM-4.5';
        this.MODEL_ID = 'GLM-4.5';
        this.API_KEY = process.env.API_KEY || 'sk-z2api-key-2024';
        this.SHOW_THINK_TAGS = this.parseBool(process.env.SHOW_THINK_TAGS, false);
        this.DEFAULT_STREAM = this.parseBool(process.env.DEFAULT_STREAM, false);
        this.MAX_REQUESTS_PER_MINUTE = parseInt(process.env.MAX_REQUESTS_PER_MINUTE) || 60;
        this.LOG_LEVEL = process.env.LOG_LEVEL || 'INFO';
        this.STREAM_TIMEOUT = parseInt(process.env.STREAM_TIMEOUT) || 30; // 流式响应数据接收超时（秒）
        this.COOKIES = [];
        this.loadCookies();
    }

    parseBool(value, defaultValue = false) {
        if (!value) return defaultValue;
        return ['true', '1', 'yes'].includes(value.toLowerCase());
    }

    loadCookies() {
        const cookiesStr = process.env.Z_AI_COOKIES || '';
        if (cookiesStr && cookiesStr !== 'your_z_ai_cookie_here') {
            this.COOKIES = cookiesStr.split(',')
                .map(cookie => cookie.trim())
                .filter(cookie => cookie.length > 0);
        }

        if (this.COOKIES.length === 0) {
            console.log('⚠️  Warning: No valid Z.AI cookies configured!');
            console.log('Please set Z_AI_COOKIES environment variable with comma-separated cookie values.');
            console.log('Example: Z_AI_COOKIES=cookie1,cookie2,cookie3');
        }
    }
}

const settings = new Settings();

// ==================== 日志工具 ====================
const logger = {
    debug: (msg) => settings.LOG_LEVEL === 'DEBUG' && console.log(`[DEBUG] ${new Date().toISOString()} - ${msg}`),
    info: (msg) => ['DEBUG', 'INFO'].includes(settings.LOG_LEVEL) && console.log(`[INFO] ${new Date().toISOString()} - ${msg}`),
    warning: (msg) => console.log(`[WARNING] ${new Date().toISOString()} - ${msg}`),
    error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} - ${msg}`)
};

// ==================== Cookie管理器 ====================
class CookieManager {
    constructor(cookies) {
        this.cookies = cookies || [];
        this.currentIndex = 0;
        this.failedCookies = new Set();
        
        if (this.cookies.length > 0) {
            logger.info(`Initialized CookieManager with ${cookies.length} cookies`);
        } else {
            logger.warning('CookieManager initialized with no cookies');
        }
        
        // 启动健康检查
        this.startHealthCheck();
    }

    async getNextCookie() {
        if (this.cookies.length === 0) return null;

        let attempts = 0;
        while (attempts < this.cookies.length) {
            const cookie = this.cookies[this.currentIndex];
            this.currentIndex = (this.currentIndex + 1) % this.cookies.length;

            if (!this.failedCookies.has(cookie)) {
                return cookie;
            }
            attempts++;
        }

        // 所有cookie都失败了，重置失败集合并重试
        if (this.failedCookies.size > 0) {
            logger.warning(`All ${this.cookies.length} cookies failed, resetting failed set and retrying`);
            this.failedCookies.clear();
            return this.cookies[0];
        }

        return null;
    }

    markCookieFailed(cookie) {
        this.failedCookies.add(cookie);
        logger.warning(`Marked cookie as failed: ${cookie.substring(0, 20)}...`);
    }

    markCookieSuccess(cookie) {
        if (this.failedCookies.has(cookie)) {
            this.failedCookies.delete(cookie);
            logger.info(`Cookie recovered: ${cookie.substring(0, 20)}...`);
        }
    }

    async healthCheck(cookie) {
        try {
            const urlObj = new URL(settings.UPSTREAM_URL);
            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port || 443,
                path: urlObj.pathname + (urlObj.search || ''),
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${cookie}`,
                    'Content-Type': 'application/json'
                },
                timeout: 10000
            };

            const data = {
                stream: false,
                model: settings.UPSTREAM_MODEL,
                messages: [{ role: 'user', content: 'test' }]
            };

            const response = await makeRequest(options, data);
            return response.status === 200;
        } catch (error) {
            return false;
        }
    }

    startHealthCheck() {
        setInterval(async () => {
            if (this.cookies.length > 0 && this.failedCookies.size > 0) {
                logger.info(`Running health check for ${this.failedCookies.size} failed cookies`);
                
                for (const cookie of Array.from(this.failedCookies)) {
                    if (await this.healthCheck(cookie)) {
                        this.markCookieSuccess(cookie);
                    }
                }
            }
        }, 600000); // 10分钟检查一次
    }
}

const cookieManager = new CookieManager(settings.COOKIES);

// ==================== 代理处理器 ====================
class ProxyHandler {
    transformContent(content) {
        if (!content) return content;

        logger.debug(`SHOW_THINK_TAGS setting: ${settings.SHOW_THINK_TAGS}`);

        if (!settings.SHOW_THINK_TAGS) {
            logger.debug('Removing thinking content from response');
            const originalLength = content.length;

            // 移除<details>块（思考内容）
            content = content.replace(/<details[^>]*>.*?<\/details>/gs, '');
            content = content.replace(/<details[^>]*>.*?(?=\s*[A-Z]|\s*\d|\s*$)/gs, '');
            content = content.trim();

            logger.debug(`Content length after removing thinking content: ${originalLength} -> ${content.length}`);
        } else {
            logger.debug('Keeping thinking content, converting to <think> tags');
            
            // 替换<details>为<think>
            content = content.replace(/<details[^>]*>/g, '<think>');
            content = content.replace(/<\/details>/g, '</think>');
            content = content.replace(/<summary>.*?<\/summary>/gs, '');

            // 如果没有闭合的</think>，添加它
            if (content.includes('<think>') && !content.includes('</think>')) {
                const thinkStart = content.indexOf('<think>');
                if (thinkStart !== -1) {
                    const answerMatch = content.substring(thinkStart).match(/\n\s*[A-Z0-9]/);
                    if (answerMatch) {
                        const insertPos = thinkStart + answerMatch.index;
                        content = content.substring(0, insertPos) + '</think>\n' + content.substring(insertPos);
                    } else {
                        content += '</think>';
                    }
                }
            }
        }

        return content.trim();
    }

    async handleChatCompletion(request, auth) {
        logger.debug(`=== ProxyHandler.handleChatCompletion Started ===`);
        logger.debug(`Auth type: ${auth.useLocalAuth ? 'Local' : 'Upstream'}`);

        // 如果使用本地认证，检查cookies是否配置
        if (auth.useLocalAuth && (!settings.COOKIES || settings.COOKIES.length === 0)) {
            logger.error(`Local auth requested but no cookies configured`);
            throw new Error('Service unavailable: No Z.AI cookies configured. Please set Z_AI_COOKIES environment variable.');
        }

        // 验证模型
        if (request.model !== settings.MODEL_NAME) {
            logger.error(`Model validation failed: ${request.model} != ${settings.MODEL_NAME}`);
            throw new Error(`Model '${request.model}' not supported. Use '${settings.MODEL_NAME}'`);
        }

        const isStreaming = request.stream !== undefined ? request.stream : settings.DEFAULT_STREAM;
        logger.debug(`Streaming mode: ${isStreaming} (requested: ${request.stream}, default: ${settings.DEFAULT_STREAM})`);

        if (isStreaming) {
            logger.debug(`Handling streaming response...`);
            return this.handleStreamingResponse(request, auth);
        } else {
            logger.debug(`Handling non-streaming response...`);
            return this.handleNonStreamingResponse(request, auth);
        }
    }

    async handleStreamingResponse(request, auth) {
        logger.debug(`=== handleStreamingResponse Started ===`);
        logger.debug(`Request ID: ${request.id || 'N/A'}`);
        logger.debug(`Auth type: ${auth.useLocalAuth ? 'Local Cookie' : 'Upstream Passthrough'}`);

        let headers;
        let cookie = null;

        if (auth.useLocalAuth) {
            // 使用本地cookie认证
            logger.debug(`Getting next cookie from cookie manager...`);
            cookie = await cookieManager.getNextCookie();
            if (!cookie) {
                logger.error(`No available cookies found`);
                throw new Error('No available cookies');
            }
            logger.debug(`Got cookie: ${cookie.substring(0, 20)}...`);
            headers = this.buildHeaders(cookie);
        } else {
            // 透传上游认证
            logger.debug(`Using upstream authentication passthrough`);
            headers = this.buildHeaders(null, auth.upstreamAuth);
        }

        logger.debug(`Building Z.AI request data...`);
        const requestData = this.buildZAIRequest(request);
        logger.debug(`Request data built, size: ${JSON.stringify(requestData).length} bytes`);

        return new Promise(async (resolve, reject) => {
            const chunks = [];
            let isCompleted = false;
            let totalTimeout = null;
            let dataTimeout = null;

            try {
                logger.debug(`Preparing HTTP request to upstream...`);
                const urlObj = new URL(settings.UPSTREAM_URL);
                const options = {
                    hostname: urlObj.hostname,
                    port: urlObj.port || 443,
                    path: urlObj.pathname + (urlObj.search || ''),
                    method: 'POST',
                    headers,
                    timeout: 300000
                };

                logger.debug(`HTTP request options: ${JSON.stringify({
                    hostname: options.hostname,
                    port: options.port,
                    path: options.path,
                    method: options.method,
                    timeout: options.timeout,
                    headersCount: Object.keys(headers).length
                })}`);

                logger.debug(`Making stream request to upstream...`);
                const startTime = Date.now();
                const response = await makeStreamRequest(options, requestData);
                const requestTime = Date.now() - startTime;
                logger.debug(`Stream request completed in ${requestTime}ms, status: ${response.status}`);

                logger.debug(`Checking response status: ${response.status}`);
                if (response.status === 401) {
                    logger.error(`Authentication failed with status 401`);
                    if (auth.useLocalAuth && cookie) {
                        logger.debug(`Marking cookie as failed`);
                        cookieManager.markCookieFailed(cookie);
                    }
                    throw new Error('Invalid authentication');
                }

                if (response.status !== 200) {
                    logger.error(`Upstream error with status: ${response.status}`);
                    throw new Error(`Upstream error: HTTP ${response.status}`);
                }

                logger.debug(`Response status OK (200), proceeding with stream processing`);
                if (auth.useLocalAuth && cookie) {
                    logger.debug(`Marking cookie as successful`);
                    cookieManager.markCookieSuccess(cookie);
                }

                const completionId = `chatcmpl-${generateUUID().replace(/-/g, '').substring(0, 29)}`;
                logger.debug(`Generated completion ID: ${completionId}`);
                let currentPhase = null;
                let buffer = '';

                logger.debug(`Setting up timeout mechanisms...`);
                // 设置总体超时机制防止流式请求无限等待
                totalTimeout = setTimeout(() => {
                    if (!isCompleted) {
                        isCompleted = true;
                        logger.warning('Streaming request total timeout (5min), sending final chunk');
                        // 发送最终chunk并结束
                        const finalChunk = {
                            id: completionId,
                            object: 'chat.completion.chunk',
                            created: Math.floor(Date.now() / 1000),
                            model: request.model,
                            choices: [{
                                index: 0,
                                delta: {},
                                finish_reason: 'stop'
                            }]
                        };
                        chunks.push(`data: ${JSON.stringify(finalChunk)}\n\n`);
                        chunks.push('data: [DONE]\n\n');
                        resolve(chunks);
                    }
                }, 300000); // 5分钟总体超时
                logger.debug(`Total timeout set: 300000ms (5 minutes)`);

                // 设置数据接收超时机制
                const streamTimeoutMs = settings.STREAM_TIMEOUT * 1000;
                logger.debug(`Stream data timeout: ${streamTimeoutMs}ms (${settings.STREAM_TIMEOUT}s)`);

                function resetDataTimeout() {
                    if (dataTimeout) {
                        clearTimeout(dataTimeout);
                        logger.debug(`Cleared previous data timeout`);
                    }
                    dataTimeout = setTimeout(() => {
                        if (!isCompleted) {
                            isCompleted = true;
                            clearTimeout(totalTimeout);
                            logger.warning(`Streaming data timeout after ${settings.STREAM_TIMEOUT}s of no data, ending request`);
                            logger.debug(`Chunks collected before timeout: ${chunks.length}`);
                            // 发送错误信息作为最终chunk
                            const errorChunk = {
                                id: completionId,
                                object: 'chat.completion.chunk',
                                created: Math.floor(Date.now() / 1000),
                                model: request.model,
                                choices: [{
                                    index: 0,
                                    delta: { content: `\n\n[Error: Stream timeout after ${settings.STREAM_TIMEOUT}s of no data]` },
                                    finish_reason: 'stop'
                                }]
                            };
                            chunks.push(`data: ${JSON.stringify(errorChunk)}\n\n`);
                            chunks.push('data: [DONE]\n\n');
                            logger.debug(`Timeout error chunk added, resolving with ${chunks.length} chunks`);
                            resolve(chunks);
                        }
                    }, streamTimeoutMs);
                    logger.debug(`Data timeout reset: ${streamTimeoutMs}ms`);
                }

                // 初始设置数据超时
                logger.debug(`Setting initial data timeout...`);
                resetDataTimeout();

                response.data.on('data', (chunk) => {
                    if (isCompleted) return;
                    buffer += chunk.toString();

                    let linesProcessed = 0;
                    while (buffer.includes('\n')) {
                        const lineEnd = buffer.indexOf('\n');
                        const line = buffer.substring(0, lineEnd).trim();
                        buffer = buffer.substring(lineEnd + 1);
                        linesProcessed++;

                        if (!line.startsWith('data: ')) {
                            logger.debug(`Skipping non-data line: ${line.substring(0, 50)}...`);
                            continue;
                        }

                        const payload = line.substring(6).trim();
                        logger.debug(`Processing data payload: ${payload.substring(0, 100)}...`);

                        if (payload === '[DONE]') {
                            // 发送最终chunk
                            logger.debug(`Received [DONE] signal, completing stream`);
                            isCompleted = true;
                            clearTimeout(totalTimeout);
                            if (dataTimeout) {
                                clearTimeout(dataTimeout);
                            }
                            const finalChunk = {
                                id: completionId,
                                object: 'chat.completion.chunk',
                                created: Math.floor(Date.now() / 1000),
                                model: request.model,
                                choices: [{
                                    index: 0,
                                    delta: {},
                                    finish_reason: 'stop'
                                }]
                            };
                            chunks.push(`data: ${JSON.stringify(finalChunk)}\n\n`);
                            chunks.push('data: [DONE]\n\n');
                            logger.debug(`Stream completed with [DONE], total chunks: ${chunks.length}`);
                            return;
                        }

                        try {
                            const parsed = JSON.parse(payload);
                            const data = parsed.data || {};
                            const deltaContent = data.delta_content || '';
                            const phase = data.phase || '';

                            logger.debug(`Parsed JSON data - phase: ${phase}, content length: ${deltaContent.length}`);

                            if (phase !== currentPhase) {
                                currentPhase = phase;
                                logger.debug(`Phase changed from '${currentPhase}' to '${phase}'`);
                            }

                            let shouldSendContent = true;
                            if (!settings.SHOW_THINK_TAGS && phase === 'thinking') {
                                shouldSendContent = false;
                                logger.debug(`Skipping thinking phase content (SHOW_THINK_TAGS=false)`);
                            }

                            if (deltaContent && shouldSendContent) {
                                let transformedDelta = deltaContent;

                                if (settings.SHOW_THINK_TAGS) {
                                    const beforeTransform = transformedDelta.length;
                                    transformedDelta = transformedDelta.replace(/<details[^>]*>/g, '<think>');
                                    transformedDelta = transformedDelta.replace(/<\/details>/g, '</think>');
                                    if (transformedDelta.length !== beforeTransform) {
                                        logger.debug(`Transformed think tags in content`);
                                    }
                                }

                                const openaiChunk = {
                                    id: completionId,
                                    object: 'chat.completion.chunk',
                                    created: Math.floor(Date.now() / 1000),
                                    model: request.model,
                                    choices: [{
                                        index: 0,
                                        delta: { content: transformedDelta },
                                        finish_reason: null
                                    }]
                                };

                                chunks.push(`data: ${JSON.stringify(openaiChunk)}\n\n`);
                                logger.debug(`Added content chunk #${chunks.length}, content: "${transformedDelta.substring(0, 50)}..."`);
                            } else if (!deltaContent) {
                                logger.debug(`No delta content in this chunk`);
                            }
                        } catch (e) {
                            logger.debug(`JSON decode error (skipping): ${e.message}, payload: ${payload.substring(0, 100)}...`);
                        }
                    }

                    if (linesProcessed > 0) {
                        logger.debug(`Processed ${linesProcessed} lines in this data chunk`);
                    }
                });

                response.data.on('end', () => {
                    logger.debug(`Stream 'end' event received`);
                    if (!isCompleted) {
                        isCompleted = true;
                        clearTimeout(totalTimeout);
                        if (dataTimeout) {
                            clearTimeout(dataTimeout);
                        }
                        logger.warning(`Stream ended without [DONE] signal, sending final chunk. Total chunks collected: ${chunks.length}`);
                        // 发送最终chunk
                        const finalChunk = {
                            id: completionId,
                            object: 'chat.completion.chunk',
                            created: Math.floor(Date.now() / 1000),
                            model: request.model,
                            choices: [{
                                index: 0,
                                delta: {},
                                finish_reason: 'stop'
                            }]
                        };
                        chunks.push(`data: ${JSON.stringify(finalChunk)}\n\n`);
                        chunks.push('data: [DONE]\n\n');
                        logger.debug(`Resolving with ${chunks.length} chunks after stream end`);
                        resolve(chunks);
                    } else {
                        logger.debug(`Stream ended but request already completed`);
                    }
                });

                response.data.on('error', (error) => {
                    logger.error(`Stream 'error' event received: ${error.message}`);
                    if (!isCompleted) {
                        isCompleted = true;
                        clearTimeout(totalTimeout);
                        if (dataTimeout) {
                            clearTimeout(dataTimeout);
                        }
                        logger.debug(`Rejecting promise due to stream error`);
                        reject(error);
                    } else {
                        logger.debug(`Stream error received but request already completed`);
                    }
                });

            } catch (error) {
                logger.error(`Exception in handleStreamingResponse: ${error.message}`);
                logger.error(`Error stack: ${error.stack}`);
                if (!isCompleted) {
                    isCompleted = true;
                    clearTimeout(totalTimeout);
                    if (dataTimeout) {
                        clearTimeout(dataTimeout);
                    }
                    logger.debug(`Cleaned up timeouts after exception`);
                }
                logger.error(`Streaming request error: ${error.message}`);
                if (auth.useLocalAuth && cookie) {
                    logger.debug(`Marking cookie as failed due to error`);
                    cookieManager.markCookieFailed(cookie);
                }
                logger.debug(`Rejecting promise due to exception`);
                reject(error);
            }

            logger.debug(`=== handleStreamingResponse Promise Setup Complete ===`);
        });
    }

    async handleNonStreamingResponse(request, auth) {
        let headers;
        let cookie = null;

        if (auth.useLocalAuth) {
            // 使用本地cookie认证
            cookie = await cookieManager.getNextCookie();
            if (!cookie) {
                throw new Error('No available cookies');
            }
            headers = this.buildHeaders(cookie);
        } else {
            // 透传上游认证
            headers = this.buildHeaders(null, auth.upstreamAuth);
        }

        const requestData = this.buildZAIRequest(request);

        try {
            const urlObj = new URL(settings.UPSTREAM_URL);
            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port || 443,
                path: urlObj.pathname + (urlObj.search || ''),
                method: 'POST',
                headers,
                timeout: 300000
            };

            const response = await makeStreamRequest(options, requestData);

            if (response.status === 401) {
                if (auth.useLocalAuth && cookie) {
                    cookieManager.markCookieFailed(cookie);
                }
                throw new Error('Invalid authentication');
            }

            if (response.status !== 200) {
                throw new Error(`Upstream error: HTTP ${response.status}`);
            }

            if (auth.useLocalAuth && cookie) {
                cookieManager.markCookieSuccess(cookie);
            }

            // 收集所有流式数据
            const chunks = [];
            let buffer = '';
            let isCompleted = false;

            return new Promise((resolve, reject) => {
                // 设置超时机制防止无限等待
                const timeout = setTimeout(() => {
                    if (!isCompleted) {
                        isCompleted = true;
                        logger.warning('Non-streaming request timeout, processing partial data');
                        // 即使没有收到[DONE]，也尝试处理已收集的数据
                        if (chunks.length > 0) {
                            this.processNonStreamingChunks(chunks, request)
                                .then(resolve)
                                .catch(reject);
                        } else {
                            reject(new Error('Request timeout with no data received'));
                        }
                    }
                }, 300000); // 5分钟超时

                logger.debug(`Setting up stream data event listeners...`);
                response.data.on('data', (chunk) => {
                    if (isCompleted) {
                        logger.debug(`Received data chunk but request already completed, ignoring`);
                        return;
                    }

                    const chunkSize = chunk.length;
                    logger.debug(`Received data chunk: ${chunkSize} bytes`);

                    // 收到数据，重置数据超时
                    resetDataTimeout();

                    buffer += chunk.toString();
                    logger.debug(`Buffer size after chunk: ${buffer.length} bytes`);

                    while (buffer.includes('\n')) {
                        const lineEnd = buffer.indexOf('\n');
                        const line = buffer.substring(0, lineEnd).trim();
                        buffer = buffer.substring(lineEnd + 1);

                        if (!line.startsWith('data: ')) continue;

                        const payload = line.substring(6).trim();
                        if (payload === '[DONE]') {
                            // 处理完成，生成响应
                            isCompleted = true;
                            clearTimeout(timeout);
                            this.processNonStreamingChunks(chunks, request)
                                .then(resolve)
                                .catch(reject);
                            return;
                        }

                        try {
                            const parsed = JSON.parse(payload);
                            chunks.push(parsed);

                            // 防止内存过度使用，限制chunks数量
                            if (chunks.length > 10000) {
                                logger.warning('Too many chunks received, processing partial data');
                                isCompleted = true;
                                clearTimeout(timeout);
                                this.processNonStreamingChunks(chunks, request)
                                    .then(resolve)
                                    .catch(reject);
                                return;
                            }
                        } catch (e) {
                            logger.debug(`JSON decode error (skipping): ${e.message}`);
                        }
                    }
                });

                response.data.on('error', (error) => {
                    if (!isCompleted) {
                        isCompleted = true;
                        clearTimeout(timeout);
                        reject(error);
                    }
                });

                response.data.on('end', () => {
                    if (!isCompleted) {
                        isCompleted = true;
                        clearTimeout(timeout);
                        logger.warning('Stream ended without [DONE] signal, processing collected data');
                        if (chunks.length > 0) {
                            this.processNonStreamingChunks(chunks, request)
                                .then(resolve)
                                .catch(reject);
                        } else {
                            reject(new Error('Stream ended with no data received'));
                        }
                    }
                });
            });

        } catch (error) {
            logger.error(`Request error: ${error.message}`);
            if (auth.useLocalAuth && cookie) {
                cookieManager.markCookieFailed(cookie);
            }
            throw error;
        }
    }

    async processNonStreamingChunks(chunks, request) {
        if (chunks.length === 0) {
            throw new Error('No response from upstream');
        }

        logger.info(`Total chunks received: ${chunks.length}`);

        // 根据SHOW_THINK_TAGS设置聚合内容
        let fullContent;
        if (settings.SHOW_THINK_TAGS) {
            // 包含所有内容
            fullContent = chunks
                .map(chunk => chunk.data?.delta_content || '')
                .join('');
        } else {
            // 只包含answer阶段的内容
            fullContent = chunks
                .filter(chunk => chunk.data?.phase === 'answer')
                .map(chunk => chunk.data?.delta_content || '')
                .join('');
        }

        logger.info(`Aggregated content length: ${fullContent.length}`);

        // 应用内容转换
        const transformedContent = this.transformContent(fullContent);
        logger.info(`Transformed content length: ${transformedContent.length}`);

        // 创建OpenAI兼容响应
        return {
            id: chunks[0]?.data?.id || `chatcmpl-${generateUUID().replace(/-/g, '').substring(0, 29)}`,
            object: 'chat.completion',
            created: Math.floor(Date.now() / 1000),
            model: request.model,
            choices: [{
                index: 0,
                message: {
                    role: 'assistant',
                    content: transformedContent
                },
                finish_reason: 'stop'
            }]
        };
    }

    buildZAIRequest(request) {
        return {
            stream: true, // 总是从Z.AI请求流式响应
            model: settings.UPSTREAM_MODEL,
            messages: request.messages,
            background_tasks: {
                title_generation: true,
                tags_generation: true
            },
            chat_id: generateUUID(),
            features: {
                image_generation: false,
                code_interpreter: false,
                web_search: false,
                auto_web_search: false
            },
            id: generateUUID(),
            mcp_servers: ['deep-web-search'],
            model_item: {
                id: settings.UPSTREAM_MODEL,
                name: 'GLM-4.5',
                owned_by: 'openai'
            },
            params: {},
            tool_servers: [],
            variables: {
                '{{USER_NAME}}': 'User',
                '{{USER_LOCATION}}': 'Unknown',
                '{{CURRENT_DATETIME}}': new Date().toISOString().replace('T', ' ').substring(0, 19)
            }
        };
    }

    buildHeaders(cookie, upstreamAuth = null) {
        const headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/event-stream',
            'Accept-Language': 'zh-CN',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'x-fe-version': 'prod-fe-1.0.53',
            'Origin': 'https://chat.z.ai',
            'Referer': 'https://chat.z.ai/c/069723d5-060b-404f-992c-4705f1554c4c'
        };

        if (upstreamAuth) {
            // 透传上游认证
            headers['Authorization'] = upstreamAuth;
        } else if (cookie) {
            // 使用本地cookie认证
            headers['Authorization'] = `Bearer ${cookie}`;
        }

        return headers;
    }
}

const proxyHandler = new ProxyHandler();

// ==================== HTTP服务器 ====================

// 认证验证函数
function verifyAuth(req) {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
        return { valid: false, error: 'Authorization header required' };
    }

    const token = authHeader.replace('Bearer ', '');
    if (token === settings.API_KEY) {
        // 匹配本地API_KEY，使用cookie认证
        return { valid: true, useLocalAuth: true };
    } else {
        // 不匹配本地API_KEY，透传给上游服务
        return { valid: true, useLocalAuth: false, upstreamAuth: authHeader };
    }
}

// 发送JSON响应
function sendJSON(res, statusCode, data) {
    res.writeHead(statusCode, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(data));
}

// 发送错误响应
function sendError(res, statusCode, message, type = 'internal_server_error') {
    sendJSON(res, statusCode, {
        error: {
            message,
            type,
            code: statusCode
        }
    });
}

// ==================== 路由处理 ====================

// 处理/v1/models路由
function handleModels(req, res) {
    setCORSHeaders(res);

    const models = [{
        id: settings.MODEL_ID,
        object: 'model',
        owned_by: 'z-ai'
    }];

    sendJSON(res, 200, {
        object: 'list',
        data: models
    });
}

// 处理/v1/chat/completions路由
async function handleChatCompletions(req, res) {
    setCORSHeaders(res);

    logger.debug(`=== handleChatCompletions Function Started ===`);

    // 验证认证
    logger.debug(`Verifying authentication...`);
    const auth = verifyAuth(req);
    logger.debug(`Auth result: ${JSON.stringify(auth)}`);

    if (!auth.valid) {
        logger.warning(`Authentication failed: ${auth.error}`);
        return sendError(res, 401, auth.error, 'invalid_request_error');
    }

    logger.debug(`Authentication successful. Use local auth: ${auth.useLocalAuth}`);

    try {
        // 如果使用本地认证，检查cookies是否配置
        if (auth.useLocalAuth) {
            logger.debug(`Using local authentication with cookies`);
            if (!settings.COOKIES || settings.COOKIES.length === 0) {
                logger.error(`No Z.AI cookies configured for local authentication`);
                return sendError(res, 503,
                    'Service unavailable: No Z.AI cookies configured. Please set Z_AI_COOKIES environment variable.',
                    'service_unavailable_error'
                );
            }
            logger.debug(`Available cookies count: ${settings.COOKIES.length}`);
        } else {
            logger.debug(`Using upstream authentication passthrough`);
        }

        // 验证模型
        logger.debug(`Validating model: ${req.body.model}`);
        if (req.body.model !== settings.MODEL_NAME) {
            logger.error(`Unsupported model: ${req.body.model}, expected: ${settings.MODEL_NAME}`);
            return sendError(res, 400,
                `Model '${req.body.model}' not supported. Use '${settings.MODEL_NAME}'`,
                'invalid_request_error'
            );
        }
        logger.debug(`Model validation passed`);

        logger.debug(`Calling proxyHandler.handleChatCompletion...`);
        const startTime = Date.now();
        const result = await proxyHandler.handleChatCompletion(req.body, auth);
        const endTime = Date.now();
        logger.debug(`proxyHandler.handleChatCompletion completed in ${endTime - startTime}ms`);

        // 检查是否是流式响应
        logger.debug(`Processing response...`);
        if (Array.isArray(result)) {
            // 流式响应 - result是chunks数组
            logger.debug(`Sending streaming response with ${result.length} chunks`);
            res.writeHead(200, {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            });

            let totalBytes = 0;
            for (const chunk of result) {
                res.write(chunk);
                totalBytes += chunk.length;
            }
            res.end();
            logger.debug(`Streaming response completed. Total bytes sent: ${totalBytes}`);
        } else {
            // 非流式响应
            logger.debug(`Sending non-streaming response`);
            logger.debug(`Response data: ${JSON.stringify(result, null, 2)}`);
            sendJSON(res, 200, result);
            logger.debug(`Non-streaming response sent successfully`);
        }

        logger.debug(`=== Chat Completions Request Completed Successfully ===`);

    } catch (error) {
        logger.error(`=== Chat Completions Request Failed ===`);
        logger.error(`Error message: ${error.message}`);
        logger.error(`Error stack: ${error.stack}`);

        let statusCode = 500;
        let errorType = 'internal_server_error';

        if (error.message.includes('No available cookies')) {
            logger.error(`Error type: No available cookies`);
            statusCode = 503;
            errorType = 'service_unavailable_error';
        } else if (error.message.includes('Invalid authentication')) {
            logger.error(`Error type: Invalid authentication`);
            statusCode = 401;
            errorType = 'invalid_request_error';
        } else if (error.message.includes('not supported')) {
            logger.error(`Error type: Model not supported`);
            statusCode = 400;
            errorType = 'invalid_request_error';
        } else {
            logger.error(`Error type: Internal server error`);
        }

        logger.error(`Sending error response: ${statusCode} - ${errorType}`);
        sendError(res, statusCode, error.message, errorType);
        logger.error(`=== Chat Completions Request Error Handling Completed ===`);
    }
}

// 处理/health路由
function handleHealth(req, res) {
    setCORSHeaders(res);

    sendJSON(res, 200, {
        status: 'healthy',
        model: settings.MODEL_NAME,
        cookies_configured: settings.COOKIES.length > 0,
        failed_cookies: cookieManager.failedCookies.size
    });
}

// ==================== HTTP服务器创建 ====================

// 主请求处理函数
function handleRequest(req, res) {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    const method = req.method;

    // 处理OPTIONS请求（CORS预检）
    if (method === 'OPTIONS') {
        setCORSHeaders(res);
        res.writeHead(200);
        res.end();
        return;
    }

    // 路由分发
    if (method === 'GET' && pathname === '/v1/models') {
        handleModels(req, res);
    } else if (method === 'GET' && pathname === '/health') {
        handleHealth(req, res);
    } else if (method === 'POST' && pathname === '/v1/chat/completions') {
        // 记录请求开始
        logger.debug(`=== Chat Completions Request Started ===`);
        logger.debug(`Request Method: ${method}`);
        logger.debug(`Request URL: ${req.url}`);
        logger.debug(`Request Headers: ${JSON.stringify(req.headers, null, 2)}`);
        logger.debug(`Client IP: ${req.connection.remoteAddress || req.socket.remoteAddress}`);
        logger.debug(`User-Agent: ${req.headers['user-agent'] || 'Unknown'}`);

        // 解析JSON请求体
        parseJSON(req, (error) => {
            if (error) {
                logger.error(`JSON parsing error: ${error.message}`);
                sendError(res, 400, 'Invalid JSON in request body', 'invalid_request_error');
                return;
            }

            // 记录请求体内容
            logger.debug(`Request Body: ${JSON.stringify(req.body, null, 2)}`);
            logger.debug(`Request Body Size: ${JSON.stringify(req.body).length} bytes`);

            // 提取关键信息
            const { model, messages, stream, temperature, max_tokens } = req.body;
            logger.debug(`Model: ${model}`);
            logger.debug(`Messages Count: ${messages ? messages.length : 0}`);
            logger.debug(`Stream Mode: ${stream}`);
            logger.debug(`Temperature: ${temperature || 'default'}`);
            logger.debug(`Max Tokens: ${max_tokens || 'default'}`);

            if (messages && messages.length > 0) {
                logger.debug(`First Message: ${JSON.stringify(messages[0])}`);
                logger.debug(`Last Message: ${JSON.stringify(messages[messages.length - 1])}`);
            }

            handleChatCompletions(req, res);
        });
    } else {
        // 404 Not Found
        setCORSHeaders(res);
        sendError(res, 404, 'Not Found', 'not_found_error');
    }
}

// 创建HTTP服务器
const server = http.createServer(handleRequest);

// 启动服务器
server.listen(settings.PORT, settings.HOST, () => {
    console.log(`🚀 Z.AI Proxy Server started successfully!`);
    console.log(`📡 Server running at http://${settings.HOST}:${settings.PORT}`);
    console.log(`📁 Working directory: ${process.cwd()}`);
    console.log(`🔑 API Key: ${settings.API_KEY}`);
    console.log(`🤖 Model: ${settings.MODEL_NAME}`);
    console.log(`🍪 Cookies configured: ${settings.COOKIES.length}`);
    console.log(`📊 Show think tags: ${settings.SHOW_THINK_TAGS}`);
    console.log(`🌊 Default streaming: ${settings.DEFAULT_STREAM}`);
    console.log(`⏱️  Stream timeout: ${settings.STREAM_TIMEOUT}s`);
    console.log(`📝 Log level: ${settings.LOG_LEVEL}`);
    console.log('');
    console.log('📋 Available endpoints:');
    console.log(`  GET  /v1/models`);
    console.log(`  POST /v1/chat/completions`);
    console.log(`  GET  /health`);
    console.log('');

    if (settings.COOKIES.length === 0) {
        console.log('⚠️  WARNING: No Z.AI cookies configured!');
        console.log('   Please set Z_AI_COOKIES environment variable.');
        console.log('   Example: Z_AI_COOKIES=cookie1,cookie2,cookie3');
    } else {
        console.log('✅ Ready to proxy requests to Z.AI!');
    }
});

// 强制退出处理
process.on('SIGTERM', () => {
    console.log('🛑 Received SIGTERM, exiting immediately...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('🛑 Received SIGINT, exiting immediately...');
    process.exit(0);
});
